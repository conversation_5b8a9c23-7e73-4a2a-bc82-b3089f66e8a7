-- =====================================================
-- ENHANCED AUTOMOTIVE DATABASE SCHEMA - FIXED VERSION
-- Supports Vehicle Sales, Repair/Maintenance Services,
-- AI Car Recommendations, Chatbot Support, and Restoration Quality Assessment
-- Version: 2.1 - MySQL Compatibility Fixed
-- =====================================================

-- Set SQL mode for compatibility
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- 1. USER MANAGEMENT TABLES
-- =====================================================

-- User roles lookup table
CREATE TABLE user_roles (
    role_id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Main users table
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Germany',
    date_of_birth DATE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES user_roles(role_id)
);

-- User sessions for authentication
CREATE TABLE user_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 2. VEHICLE MANAGEMENT TABLES
-- =====================================================

-- Car categories (SUV, Sedan, Hatchback, etc.)
CREATE TABLE car_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Car brands (Audi, BMW, Mercedes, etc.)
CREATE TABLE car_brands (
    brand_id INT PRIMARY KEY AUTO_INCREMENT,
    brand_name VARCHAR(100) NOT NULL UNIQUE,
    logo_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Car models (A4, A6, Q5, etc.)
CREATE TABLE car_models (
    model_id INT PRIMARY KEY AUTO_INCREMENT,
    brand_id INT NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES car_brands(brand_id),
    FOREIGN KEY (category_id) REFERENCES car_categories(category_id),
    UNIQUE KEY unique_brand_model (brand_id, model_name)
);

-- Main cars table (inventory) - Remove circular dependencies temporarily
CREATE TABLE cars (
    car_id INT PRIMARY KEY AUTO_INCREMENT,
    model_id INT NOT NULL,
    vin VARCHAR(17) UNIQUE,
    year INT NOT NULL,
    color VARCHAR(50),
    fuel_type ENUM('Gasoline', 'Diesel', 'Electric', 'Hybrid', 'Plug-in Hybrid') NOT NULL,
    transmission ENUM('Manual', 'Automatic', 'CVT') NOT NULL,
    engine_size DECIMAL(3,1),
    mileage INT DEFAULT 0,
    condition_type ENUM('New', 'Used', 'Certified Pre-Owned') NOT NULL,
    condition_description TEXT,
    price DECIMAL(12,2) NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    location VARCHAR(255),
    description TEXT,
    features JSON,
    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_sold TIMESTAMP NULL,
    sold_to_user_id INT NULL,
    created_by_admin_id INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES car_models(model_id),
    INDEX idx_price (price),
    INDEX idx_condition (condition_type),
    INDEX idx_fuel_type (fuel_type),
    INDEX idx_available (is_available),
    INDEX idx_year (year)
);

-- Car images table
CREATE TABLE car_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 1,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id) ON DELETE CASCADE,
    INDEX idx_primary (car_id, is_primary)
);

-- 3. AI CAR RECOMMENDATION SYSTEM
-- =====================================================

-- Customer preferences for AI recommendations
CREATE TABLE customer_preferences (
    preference_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    budget_min DECIMAL(12,2),
    budget_max DECIMAL(12,2),
    preferred_fuel_types JSON,
    preferred_categories JSON,
    preferred_brands JSON,
    preferred_transmission ENUM('Manual', 'Automatic', 'CVT', 'Any') DEFAULT 'Any',
    max_mileage INT,
    min_year INT,
    required_features JSON,
    preferred_colors JSON,
    family_size INT,
    usage_type ENUM('Daily Commute', 'Family', 'Business', 'Sport', 'Luxury') DEFAULT 'Daily Commute',
    driving_frequency ENUM('Daily', 'Weekly', 'Occasional') DEFAULT 'Daily',
    parking_constraints ENUM('Garage', 'Street', 'Compact Space', 'No Constraints') DEFAULT 'No Constraints',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_budget (budget_min, budget_max)
);

-- AI recommendation sessions and results
CREATE TABLE ai_recommendations (
    recommendation_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    preference_id INT,
    session_id VARCHAR(255),
    car_id INT NOT NULL,
    recommendation_score DECIMAL(5,3),
    match_reasons JSON,
    ai_model_version VARCHAR(50),
    recommendation_rank INT,
    user_feedback ENUM('Liked', 'Disliked', 'Viewed', 'Contacted', 'Test Drove', 'Purchased') NULL,
    feedback_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    feedback_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (preference_id) REFERENCES customer_preferences(preference_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    INDEX idx_user_session (user_id, session_id),
    INDEX idx_score (recommendation_score DESC),
    INDEX idx_feedback (user_feedback)
);

-- User interaction tracking for AI learning
CREATE TABLE user_car_interactions (
    interaction_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    interaction_type ENUM('View', 'Like', 'Save', 'Share', 'Compare', 'Contact', 'Test Drive Request', 'Purchase Inquiry') NOT NULL,
    interaction_duration_seconds INT,
    device_type ENUM('Desktop', 'Mobile', 'Tablet') DEFAULT 'Desktop',
    referral_source VARCHAR(255),
    session_id VARCHAR(255),
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    INDEX idx_user_type (user_id, interaction_type),
    INDEX idx_car_interactions (car_id, interaction_type),
    INDEX idx_session (session_id)
);

-- Car comparison feature for AI insights
CREATE TABLE car_comparisons (
    comparison_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    comparison_name VARCHAR(255),
    car_ids JSON NOT NULL,
    comparison_criteria JSON,
    notes TEXT,
    is_saved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_saved (user_id, is_saved)
);

-- 4. CHATBOT SUPPORT SYSTEM
-- =====================================================

-- Chatbot conversation sessions
CREATE TABLE chatbot_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id INT NULL,
    session_type ENUM('Sales Inquiry', 'Service Support', 'General Info', 'Technical Support', 'Complaint') DEFAULT 'General Info',
    language_code VARCHAR(5) DEFAULT 'en',
    is_active BOOLEAN DEFAULT TRUE,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    satisfaction_rating INT NULL,
    escalated_to_human BOOLEAN DEFAULT FALSE,
    escalated_at TIMESTAMP NULL,
    assigned_agent_id INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_type_active (session_type, is_active)
);

-- Individual chatbot messages
CREATE TABLE chatbot_messages (
    message_id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(255) NOT NULL,
    sender_type ENUM('User', 'Bot', 'Agent') NOT NULL,
    sender_id INT NULL,
    message_text TEXT NOT NULL,
    message_type ENUM('Text', 'Quick Reply', 'Card', 'Image', 'File', 'Location') DEFAULT 'Text',
    intent_detected VARCHAR(255),
    confidence_score DECIMAL(5,3),
    entities_extracted JSON,
    suggested_actions JSON,
    attachments JSON,
    is_sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chatbot_sessions(session_id) ON DELETE CASCADE,
    INDEX idx_session_time (session_id, created_at),
    INDEX idx_intent (intent_detected),
    INDEX idx_sender (sender_type, sender_id)
);

-- Chatbot knowledge base for responses
CREATE TABLE chatbot_knowledge_base (
    kb_id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(100) NOT NULL,
    intent VARCHAR(255) NOT NULL,
    question_patterns JSON NOT NULL,
    response_text TEXT NOT NULL,
    response_type ENUM('Text', 'Quick Reply', 'Card', 'Redirect') DEFAULT 'Text',
    follow_up_questions JSON,
    required_entities JSON,
    priority_score INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_intent_active (intent, is_active),
    INDEX idx_category (category),
    INDEX idx_priority (priority_score DESC)
);

-- Chatbot escalation rules
CREATE TABLE chatbot_escalation_rules (
    rule_id INT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(255) NOT NULL,
    trigger_conditions JSON NOT NULL,
    escalation_type ENUM('Immediate', 'Queue', 'Callback', 'Email') DEFAULT 'Queue',
    target_department ENUM('Sales', 'Service', 'Technical', 'Management') DEFAULT 'Sales',
    priority_level ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_active (is_active)
);

-- 5. SALES & CUSTOMER INTEREST TABLES
-- =====================================================

-- Test drive requests
CREATE TABLE test_drive_requests (
    request_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    requested_date DATE NOT NULL,
    requested_time TIME NOT NULL,
    status ENUM('Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled') DEFAULT 'Pending',
    notes TEXT,
    admin_notes TEXT,
    approved_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    INDEX idx_status (status),
    INDEX idx_date (requested_date)
);

-- Purchase/reservation requests
CREATE TABLE purchase_requests (
    request_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    request_type ENUM('Purchase', 'Reservation') NOT NULL,
    offered_price DECIMAL(12,2),
    financing_required BOOLEAN DEFAULT FALSE,
    trade_in_car_details TEXT,
    status ENUM('Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled') DEFAULT 'Pending',
    admin_notes TEXT,
    processed_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    INDEX idx_status (status),
    INDEX idx_type (request_type)
);

-- 6. RESTORATION QUALITY ASSESSMENT SYSTEM
-- =====================================================

-- Restoration projects tracking
CREATE TABLE restoration_projects (
    project_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    customer_id INT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    project_type ENUM('Full Restoration', 'Partial Restoration', 'Repair', 'Customization', 'Maintenance') NOT NULL,
    project_status ENUM('Planning', 'In Progress', 'Quality Review', 'Completed', 'On Hold', 'Cancelled') DEFAULT 'Planning',
    estimated_start_date DATE,
    actual_start_date DATE,
    estimated_completion_date DATE,
    actual_completion_date DATE,
    estimated_cost DECIMAL(12,2),
    actual_cost DECIMAL(12,2),
    project_description TEXT,
    special_requirements TEXT,
    assigned_team_lead_id INT,
    quality_score DECIMAL(4,2),
    customer_satisfaction_rating INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (customer_id) REFERENCES users(user_id),
    INDEX idx_status (project_status),
    INDEX idx_customer (customer_id),
    INDEX idx_dates (estimated_completion_date, actual_completion_date)
);

-- Restoration phases/stages
CREATE TABLE restoration_phases (
    phase_id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    phase_name VARCHAR(255) NOT NULL,
    phase_order INT NOT NULL,
    phase_description TEXT,
    estimated_duration_days INT,
    actual_duration_days INT,
    phase_status ENUM('Not Started', 'In Progress', 'Completed', 'On Hold', 'Skipped') DEFAULT 'Not Started',
    quality_rating DECIMAL(4,2),
    notes TEXT,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    assigned_technician_id INT,
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    INDEX idx_project_order (project_id, phase_order),
    INDEX idx_status (phase_status)
);

-- Before/After photo management for quality assessment
CREATE TABLE restoration_photos (
    photo_id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    phase_id INT NULL,
    photo_type ENUM('Before', 'During', 'After', 'Detail', 'Problem', 'Solution') NOT NULL,
    photo_category ENUM('Exterior', 'Interior', 'Engine', 'Undercarriage', 'Wheels', 'Paint', 'Upholstery', 'Dashboard', 'Other') NOT NULL,
    photo_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    photo_description TEXT,
    photo_angle VARCHAR(100),
    lighting_conditions VARCHAR(100),
    camera_settings JSON,
    ai_quality_score DECIMAL(5,3),
    ai_damage_assessment JSON,
    ai_completion_percentage DECIMAL(5,2),
    is_featured BOOLEAN DEFAULT FALSE,
    taken_by_user_id INT NOT NULL,
    taken_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (phase_id) REFERENCES restoration_phases(phase_id),
    FOREIGN KEY (taken_by_user_id) REFERENCES users(user_id),
    INDEX idx_project_type (project_id, photo_type),
    INDEX idx_category (photo_category),
    INDEX idx_quality (ai_quality_score DESC)
);

-- Quality assessment criteria and scoring
CREATE TABLE quality_assessment_criteria (
    criteria_id INT PRIMARY KEY AUTO_INCREMENT,
    criteria_name VARCHAR(255) NOT NULL,
    criteria_category ENUM('Paint', 'Body', 'Interior', 'Mechanical', 'Electrical', 'Overall') NOT NULL,
    description TEXT,
    weight_percentage DECIMAL(5,2) NOT NULL,
    scoring_method ENUM('Numeric', 'Pass/Fail', 'Percentage', 'Grade') DEFAULT 'Numeric',
    min_score DECIMAL(4,2) DEFAULT 0.00,
    max_score DECIMAL(4,2) DEFAULT 10.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (criteria_category),
    INDEX idx_active (is_active)
);

-- Individual quality assessments
CREATE TABLE quality_assessments (
    assessment_id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    phase_id INT NULL,
    criteria_id INT NOT NULL,
    assessor_id INT NOT NULL,
    assessment_type ENUM('Self Assessment', 'Peer Review', 'Customer Review', 'Final Inspection') NOT NULL,
    score DECIMAL(4,2) NOT NULL,
    notes TEXT,
    before_photo_id INT NULL,
    after_photo_id INT NULL,
    assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (phase_id) REFERENCES restoration_phases(phase_id),
    FOREIGN KEY (criteria_id) REFERENCES quality_assessment_criteria(criteria_id),
    FOREIGN KEY (assessor_id) REFERENCES users(user_id),
    FOREIGN KEY (before_photo_id) REFERENCES restoration_photos(photo_id),
    FOREIGN KEY (after_photo_id) REFERENCES restoration_photos(photo_id),
    INDEX idx_project_type (project_id, assessment_type),
    INDEX idx_score (score),
    INDEX idx_date (assessment_date)
);

-- AI-powered photo comparison and analysis
CREATE TABLE ai_photo_analysis (
    analysis_id INT PRIMARY KEY AUTO_INCREMENT,
    before_photo_id INT NOT NULL,
    after_photo_id INT NOT NULL,
    project_id INT NOT NULL,
    ai_model_version VARCHAR(50) NOT NULL,
    improvement_score DECIMAL(5,3),
    detected_improvements JSON,
    detected_issues JSON,
    color_accuracy_score DECIMAL(5,3),
    surface_quality_score DECIMAL(5,3),
    alignment_score DECIMAL(5,3),
    completeness_score DECIMAL(5,3),
    overall_quality_score DECIMAL(5,3),
    confidence_level DECIMAL(5,3),
    processing_time_ms INT,
    analysis_metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (before_photo_id) REFERENCES restoration_photos(photo_id),
    FOREIGN KEY (after_photo_id) REFERENCES restoration_photos(photo_id),
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    INDEX idx_project (project_id),
    INDEX idx_quality (overall_quality_score DESC),
    INDEX idx_confidence (confidence_level DESC)
);

-- 7. SERVICE & REPAIR MANAGEMENT TABLES
-- =====================================================

-- Service categories (Maintenance, Repair, Inspection, etc.)
CREATE TABLE service_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Available service types
CREATE TABLE service_types (
    service_id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    service_name VARCHAR(150) NOT NULL,
    description TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    estimated_duration_hours DECIMAL(4,2),
    is_active BOOLEAN DEFAULT TRUE,
    requires_appointment BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES service_categories(category_id),
    INDEX idx_active (is_active),
    INDEX idx_price (base_price)
);

-- Service/repair appointments/bookings
CREATE TABLE service_bookings (
    booking_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    car_vin VARCHAR(17),
    car_make VARCHAR(100),
    car_model VARCHAR(100),
    car_year INT,
    scheduled_date DATE NOT NULL,
    scheduled_time TIME NOT NULL,
    estimated_completion TIMESTAMP,
    actual_completion TIMESTAMP NULL,
    status ENUM('Pending', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'No Show') DEFAULT 'Pending',
    priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    problem_description TEXT,
    customer_notes TEXT,
    mechanic_notes TEXT,
    final_cost DECIMAL(10,2),
    assigned_mechanic_id INT NULL,
    approved_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES service_types(service_id),
    INDEX idx_status (status),
    INDEX idx_date (scheduled_date),
    INDEX idx_mechanic (assigned_mechanic_id)
);

-- Service booking status history (for tracking)
CREATE TABLE booking_status_history (
    history_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    previous_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    changed_by_user_id INT NOT NULL,
    notes TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by_user_id) REFERENCES users(user_id)
);

-- Images uploaded for service issues
CREATE TABLE service_issue_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_description TEXT,
    uploaded_by_user_id INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(user_id)
);

-- 8. FINANCIAL & PAYMENT MANAGEMENT
-- =====================================================

-- Payment methods
CREATE TABLE payment_methods (
    payment_method_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    method_type ENUM('Credit Card', 'Debit Card', 'Bank Transfer', 'PayPal', 'Financing', 'Cash') NOT NULL,
    provider VARCHAR(100),
    last_four_digits VARCHAR(4),
    expiry_month INT,
    expiry_year INT,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active)
);

-- Financial transactions
CREATE TABLE transactions (
    transaction_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    transaction_type ENUM('Car Purchase', 'Service Payment', 'Restoration Payment', 'Deposit', 'Refund', 'Fee') NOT NULL,
    related_id INT,
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'EUR',
    payment_method_id INT,
    transaction_status ENUM('Pending', 'Processing', 'Completed', 'Failed', 'Cancelled', 'Refunded') DEFAULT 'Pending',
    payment_gateway VARCHAR(100),
    gateway_transaction_id VARCHAR(255),
    description TEXT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(payment_method_id),
    INDEX idx_user_type (user_id, transaction_type),
    INDEX idx_status (transaction_status),
    INDEX idx_amount (amount)
);

-- 9. REVIEW & RATING SYSTEM
-- =====================================================

-- Service reviews and ratings
CREATE TABLE service_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL,
    review_title VARCHAR(200),
    review_text TEXT,
    would_recommend BOOLEAN,
    is_verified BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    UNIQUE KEY unique_user_booking (user_id, booking_id),
    INDEX idx_rating (rating),
    INDEX idx_verified (is_verified)
);

-- Car/sales reviews (for purchased cars)
CREATE TABLE car_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    user_id INT NOT NULL,
    purchase_request_id INT NULL,
    rating INT NOT NULL,
    review_title VARCHAR(200),
    review_text TEXT,
    would_recommend BOOLEAN,
    is_verified BOOLEAN DEFAULT FALSE,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(request_id),
    INDEX idx_rating (rating),
    INDEX idx_verified (is_verified)
);

-- 10. SYSTEM CONFIGURATION & NOTIFICATIONS
-- =====================================================

-- System settings/configuration
CREATE TABLE system_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_description TEXT,
    updated_by_admin_id INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Notifications for users
CREATE TABLE notifications (
    notification_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    notification_type ENUM('Test Drive', 'Purchase', 'Service', 'System', 'Review') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_type (notification_type)
);

-- =====================================================
-- ADD MISSING FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add foreign keys that were temporarily removed
ALTER TABLE cars ADD CONSTRAINT fk_cars_sold_to_user FOREIGN KEY (sold_to_user_id) REFERENCES users(user_id);
ALTER TABLE cars ADD CONSTRAINT fk_cars_created_by_admin FOREIGN KEY (created_by_admin_id) REFERENCES users(user_id);
ALTER TABLE test_drive_requests ADD CONSTRAINT fk_test_drive_approved_by FOREIGN KEY (approved_by_admin_id) REFERENCES users(user_id);
ALTER TABLE purchase_requests ADD CONSTRAINT fk_purchase_processed_by FOREIGN KEY (processed_by_admin_id) REFERENCES users(user_id);
ALTER TABLE restoration_projects ADD CONSTRAINT fk_restoration_team_lead FOREIGN KEY (assigned_team_lead_id) REFERENCES users(user_id);
ALTER TABLE restoration_phases ADD CONSTRAINT fk_restoration_technician FOREIGN KEY (assigned_technician_id) REFERENCES users(user_id);
ALTER TABLE service_bookings ADD CONSTRAINT fk_service_mechanic FOREIGN KEY (assigned_mechanic_id) REFERENCES users(user_id);
ALTER TABLE service_bookings ADD CONSTRAINT fk_service_approved_by FOREIGN KEY (approved_by_admin_id) REFERENCES users(user_id);
ALTER TABLE chatbot_sessions ADD CONSTRAINT fk_chatbot_assigned_agent FOREIGN KEY (assigned_agent_id) REFERENCES users(user_id);
ALTER TABLE chatbot_messages ADD CONSTRAINT fk_chatbot_sender FOREIGN KEY (sender_id) REFERENCES users(user_id);
ALTER TABLE chatbot_knowledge_base ADD CONSTRAINT fk_chatbot_kb_created_by FOREIGN KEY (created_by_admin_id) REFERENCES users(user_id);
ALTER TABLE system_settings ADD CONSTRAINT fk_system_settings_updated_by FOREIGN KEY (updated_by_admin_id) REFERENCES users(user_id);

-- Add check constraints for ratings
ALTER TABLE service_reviews ADD CONSTRAINT chk_service_rating CHECK (rating >= 1 AND rating <= 5);
ALTER TABLE car_reviews ADD CONSTRAINT chk_car_rating CHECK (rating >= 1 AND rating <= 5);
ALTER TABLE chatbot_sessions ADD CONSTRAINT chk_satisfaction_rating CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5);
ALTER TABLE restoration_projects ADD CONSTRAINT chk_customer_satisfaction CHECK (customer_satisfaction_rating >= 1 AND customer_satisfaction_rating <= 5);

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default user roles
INSERT INTO user_roles (role_name, description) VALUES
('Admin', 'Full system access and management'),
('Customer', 'Regular customer with basic access'),
('Mechanic', 'Service technician with repair access'),
('Sales', 'Sales representative with car management access');

-- Insert default service categories
INSERT INTO service_categories (category_name, description) VALUES
('Maintenance', 'Regular maintenance services'),
('Engine Repair', 'Engine-related repairs and diagnostics'),
('Body Work', 'Painting, dent repair, and body services'),
('Electrical', 'Electrical system diagnostics and repair'),
('Transmission', 'Transmission services and repair'),
('Inspection', 'Safety and emissions inspections');

-- Insert sample service types
INSERT INTO service_types (category_id, service_name, description, base_price, estimated_duration_hours) VALUES
(1, 'Oil Change', 'Standard oil change with filter replacement', 75.00, 0.5),
(1, 'Brake Inspection', 'Complete brake system inspection', 95.00, 1.0),
(2, 'Engine Diagnostics', 'Computer diagnostics for engine issues', 125.00, 1.5),
(2, 'Engine Repair', 'General engine repair services', 350.00, 4.0),
(3, 'Paint Touch-up', 'Minor paint touch-up and scratch repair', 200.00, 2.0),
(3, 'Dent Repair', 'Paintless dent repair service', 150.00, 1.5),
(4, 'Electrical Diagnostics', 'Electrical system troubleshooting', 110.00, 1.0),
(5, 'Transmission Service', 'Transmission fluid change and service', 180.00, 2.0),
(6, 'Safety Inspection', 'Annual safety inspection', 50.00, 0.5);

-- Insert sample car categories
INSERT INTO car_categories (category_name, description) VALUES
('Sedan', 'Four-door passenger cars'),
('SUV', 'Sport Utility Vehicles'),
('Hatchback', 'Compact cars with rear hatch'),
('Coupe', 'Two-door sports cars'),
('Convertible', 'Open-top vehicles'),
('Station Wagon', 'Family-oriented cargo vehicles');

-- Insert sample car brands
INSERT INTO car_brands (brand_name) VALUES
('Audi'),
('BMW'),
('Mercedes-Benz'),
('Volkswagen'),
('Porsche');

-- Insert some sample Audi models
INSERT INTO car_models (brand_id, model_name, category_id) VALUES
(1, 'A3', 3), -- Audi A3 Hatchback
(1, 'A4', 1), -- Audi A4 Sedan
(1, 'A6', 1), -- Audi A6 Sedan
(1, 'Q3', 2), -- Audi Q3 SUV
(1, 'Q5', 2), -- Audi Q5 SUV
(1, 'Q7', 2), -- Audi Q7 SUV
(1, 'TT', 4), -- Audi TT Coupe
(1, 'R8', 4); -- Audi R8 Coupe

-- Insert enhanced system settings
INSERT INTO system_settings (setting_key, setting_value, setting_description) VALUES
('site_name', 'Audi Dealership', 'Name of the dealership'),
('max_test_drives_per_month', '3', 'Maximum test drives per customer per month'),
('service_booking_advance_days', '30', 'How many days in advance can customers book services'),
('featured_cars_limit', '12', 'Number of featured cars to display on homepage'),
('ai_recommendation_enabled', 'true', 'Enable AI car recommendation system'),
('ai_recommendation_max_results', '10', 'Maximum number of AI recommendations to show'),
('chatbot_enabled', 'true', 'Enable chatbot support system'),
('chatbot_escalation_threshold', '3', 'Number of failed responses before escalating to human'),
('restoration_quality_threshold', '8.0', 'Minimum quality score for restoration completion'),
('ai_photo_analysis_enabled', 'true', 'Enable AI-powered photo analysis for restorations');

-- Insert default quality assessment criteria
INSERT INTO quality_assessment_criteria (criteria_name, criteria_category, description, weight_percentage, max_score) VALUES
('Paint Quality', 'Paint', 'Overall paint finish, color match, and surface smoothness', 25.00, 10.00),
('Body Alignment', 'Body', 'Panel gaps, alignment, and structural integrity', 20.00, 10.00),
('Interior Condition', 'Interior', 'Upholstery, dashboard, and interior components', 15.00, 10.00),
('Engine Performance', 'Mechanical', 'Engine operation, performance, and reliability', 20.00, 10.00),
('Electrical Systems', 'Electrical', 'All electrical components and systems functionality', 10.00, 10.00),
('Overall Craftsmanship', 'Overall', 'General workmanship and attention to detail', 10.00, 10.00);

-- Insert sample chatbot knowledge base entries
INSERT INTO chatbot_knowledge_base (category, intent, question_patterns, response_text, response_type) VALUES
('Sales', 'car_inquiry', '["What cars do you have?", "Show me available cars", "Car inventory"]', 'I can help you find the perfect car! What type of vehicle are you looking for? We have sedans, SUVs, coupes, and more available.', 'Text'),
('Sales', 'price_inquiry', '["How much does it cost?", "What is the price?", "Car pricing"]', 'Car prices vary based on model, year, and condition. Would you like me to show you cars within a specific budget range?', 'Text'),
('Service', 'service_booking', '["Book service", "Schedule appointment", "Service booking"]', 'I can help you schedule a service appointment. What type of service do you need? Oil change, brake inspection, or something else?', 'Text'),
('Service', 'service_hours', '["What are your hours?", "When are you open?", "Service hours"]', 'Our service department is open Monday-Friday 8AM-6PM, and Saturday 8AM-4PM. We are closed on Sundays.', 'Text'),
('General', 'greeting', '["Hello", "Hi", "Hey", "Good morning"]', 'Hello! Welcome to Audi Dealership. I am here to help you with car sales, service appointments, and general inquiries. How can I assist you today?', 'Text');

-- Insert sample escalation rules
INSERT INTO chatbot_escalation_rules (rule_name, trigger_conditions, escalation_type, target_department, priority_level) VALUES
('Complex Technical Issues', '{"keywords": ["engine problem", "transmission issue", "electrical fault"], "confidence_threshold": 0.3}', 'Queue', 'Technical', 'High'),
('Purchase Intent', '{"keywords": ["buy", "purchase", "financing"], "confidence_threshold": 0.7}', 'Immediate', 'Sales', 'Medium'),
('Service Complaints', '{"keywords": ["complaint", "unsatisfied", "poor service"], "sentiment": "negative"}', 'Immediate', 'Management', 'High'),
('Multiple Failed Responses', '{"failed_responses": 3}', 'Queue', 'Sales', 'Medium');

-- =====================================================
-- USEFUL INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional composite indexes for common queries
CREATE INDEX idx_cars_search ON cars (condition_type, fuel_type, price, is_available);
CREATE INDEX idx_bookings_schedule ON service_bookings (scheduled_date, scheduled_time, status);
CREATE INDEX idx_reviews_rating_date ON service_reviews (rating, created_at);
CREATE INDEX idx_user_email_active ON users (email, is_active);

-- AI and enhanced feature indexes
CREATE INDEX idx_ai_recommendations_score_rank ON ai_recommendations (recommendation_score DESC, recommendation_rank);
CREATE INDEX idx_user_interactions_type_time ON user_car_interactions (user_id, interaction_type, created_at);
CREATE INDEX idx_chatbot_sessions_active_type ON chatbot_sessions (is_active, session_type, started_at);
CREATE INDEX idx_chatbot_messages_session_time ON chatbot_messages (session_id, created_at);
CREATE INDEX idx_restoration_projects_status_customer ON restoration_projects (project_status, customer_id);
CREATE INDEX idx_restoration_photos_project_type ON restoration_photos (project_id, photo_type, taken_at);
CREATE INDEX idx_quality_assessments_project_score ON quality_assessments (project_id, score DESC);
CREATE INDEX idx_transactions_user_status_amount ON transactions (user_id, transaction_status, amount);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for car listings with full details
CREATE VIEW car_listings_view AS
SELECT
    c.car_id,
    c.vin,
    b.brand_name,
    m.model_name,
    cat.category_name,
    c.year,
    c.color,
    c.fuel_type,
    c.transmission,
    c.engine_size,
    c.mileage,
    c.condition_type,
    c.price,
    c.is_available,
    c.is_featured,
    c.description,
    c.features,
    c.date_added,
    (SELECT image_url FROM car_images ci WHERE ci.car_id = c.car_id AND ci.is_primary = TRUE LIMIT 1) as primary_image
FROM cars c
JOIN car_models m ON c.model_id = m.model_id
JOIN car_brands b ON m.brand_id = b.brand_id
JOIN car_categories cat ON m.category_id = cat.category_id;

-- View for service bookings with user and service details
CREATE VIEW service_bookings_view AS
SELECT
    sb.booking_id,
    sb.scheduled_date,
    sb.scheduled_time,
    sb.status,
    sb.priority,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    u.email as customer_email,
    u.phone as customer_phone,
    st.service_name,
    sc.category_name as service_category,
    sb.final_cost,
    CONCAT(mech.first_name, ' ', mech.last_name) as mechanic_name,
    sb.created_at,
    sb.updated_at
FROM service_bookings sb
JOIN users u ON sb.user_id = u.user_id
JOIN service_types st ON sb.service_id = st.service_id
JOIN service_categories sc ON st.category_id = sc.category_id
LEFT JOIN users mech ON sb.assigned_mechanic_id = mech.user_id;

-- View for AI recommendations with car details
CREATE VIEW ai_recommendations_view AS
SELECT
    ar.recommendation_id,
    ar.user_id,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    ar.session_id,
    ar.recommendation_score,
    ar.recommendation_rank,
    ar.match_reasons,
    ar.user_feedback,
    c.car_id,
    b.brand_name,
    m.model_name,
    cat.category_name,
    c.year,
    c.price,
    c.fuel_type,
    c.condition_type,
    ar.created_at
FROM ai_recommendations ar
JOIN users u ON ar.user_id = u.user_id
JOIN cars c ON ar.car_id = c.car_id
JOIN car_models m ON c.model_id = m.model_id
JOIN car_brands b ON m.brand_id = b.brand_id
JOIN car_categories cat ON m.category_id = cat.category_id;

-- View for restoration projects with quality metrics
CREATE VIEW restoration_projects_view AS
SELECT
    rp.project_id,
    rp.project_name,
    rp.project_type,
    rp.project_status,
    CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
    CONCAT(lead.first_name, ' ', lead.last_name) as team_lead_name,
    b.brand_name,
    m.model_name,
    c.year,
    rp.estimated_cost,
    rp.actual_cost,
    rp.quality_score,
    rp.customer_satisfaction_rating,
    rp.estimated_completion_date,
    rp.actual_completion_date,
    (SELECT COUNT(*) FROM restoration_photos rph WHERE rph.project_id = rp.project_id AND rph.photo_type = 'Before') as before_photos_count,
    (SELECT COUNT(*) FROM restoration_photos rph WHERE rph.project_id = rp.project_id AND rph.photo_type = 'After') as after_photos_count,
    (SELECT AVG(qa.score) FROM quality_assessments qa WHERE qa.project_id = rp.project_id) as avg_quality_score,
    rp.created_at,
    rp.updated_at
FROM restoration_projects rp
JOIN cars c ON rp.car_id = c.car_id
JOIN car_models m ON c.model_id = m.model_id
JOIN car_brands b ON m.brand_id = b.brand_id
JOIN users customer ON rp.customer_id = customer.user_id
LEFT JOIN users lead ON rp.assigned_team_lead_id = lead.user_id;
