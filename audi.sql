-- =====================================================
-- CAR COMPANY DATABASE SCHEMA
-- Supports Vehicle Sales & Repair/Maintenance Services
-- =====================================================

-- 1. USER MANAGEMENT TABLES
-- =====================================================

-- User roles lookup table
CREATE TABLE user_roles (
    role_id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Main users table
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Germany',
    date_of_birth DATE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES user_roles(role_id)
);

-- User sessions for authentication
CREATE TABLE user_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 2. VEHICLE MANAGEMENT TABLES
-- =====================================================

-- Car categories (SUV, Sedan, Hatchback, etc.)
CREATE TABLE car_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Car brands (Audi, BMW, Mercedes, etc.)
CREATE TABLE car_brands (
    brand_id INT PRIMARY KEY AUTO_INCREMENT,
    brand_name VARCHAR(100) NOT NULL UNIQUE,
    logo_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Car models (A4, A6, Q5, etc.)
CREATE TABLE car_models (
    model_id INT PRIMARY KEY AUTO_INCREMENT,
    brand_id INT NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES car_brands(brand_id),
    FOREIGN KEY (category_id) REFERENCES car_categories(category_id),
    UNIQUE KEY unique_brand_model (brand_id, model_name)
);

-- Main cars table (inventory)
CREATE TABLE cars (
    car_id INT PRIMARY KEY AUTO_INCREMENT,
    model_id INT NOT NULL,
    vin VARCHAR(17) UNIQUE, -- Vehicle Identification Number
    year INT NOT NULL,
    color VARCHAR(50),
    fuel_type ENUM('Gasoline', 'Diesel', 'Electric', 'Hybrid', 'Plug-in Hybrid') NOT NULL,
    transmission ENUM('Manual', 'Automatic', 'CVT') NOT NULL,
    engine_size DECIMAL(3,1), -- e.g., 2.0 for 2.0L
    mileage INT DEFAULT 0, -- in kilometers
    condition_type ENUM('New', 'Used', 'Certified Pre-Owned') NOT NULL,
    condition_description TEXT,
    price DECIMAL(12,2) NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    location VARCHAR(255),
    description TEXT,
    features JSON, -- Store car features as JSON (e.g., ["GPS", "Leather Seats", "Sunroof"])
    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_sold TIMESTAMP NULL,
    sold_to_user_id INT NULL,
    created_by_admin_id INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES car_models(model_id),
    FOREIGN KEY (sold_to_user_id) REFERENCES users(user_id),
    FOREIGN KEY (created_by_admin_id) REFERENCES users(user_id),
    INDEX idx_price (price),
    INDEX idx_condition (condition_type),
    INDEX idx_fuel_type (fuel_type),
    INDEX idx_available (is_available),
    INDEX idx_year (year)
);

-- Car images table
CREATE TABLE car_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 1,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id) ON DELETE CASCADE,
    INDEX idx_primary (car_id, is_primary)
);

-- 3. SALES & CUSTOMER INTEREST TABLES
-- =====================================================

-- Test drive requests
CREATE TABLE test_drive_requests (
    request_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    requested_date DATE NOT NULL,
    requested_time TIME NOT NULL,
    status ENUM('Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled') DEFAULT 'Pending',
    notes TEXT,
    admin_notes TEXT,
    approved_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (approved_by_admin_id) REFERENCES users(user_id),
    INDEX idx_status (status),
    INDEX idx_date (requested_date)
);

-- Purchase/reservation requests
CREATE TABLE purchase_requests (
    request_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    request_type ENUM('Purchase', 'Reservation') NOT NULL,
    offered_price DECIMAL(12,2),
    financing_required BOOLEAN DEFAULT FALSE,
    trade_in_car_details TEXT,
    status ENUM('Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled') DEFAULT 'Pending',
    admin_notes TEXT,
    processed_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (processed_by_admin_id) REFERENCES users(user_id),
    INDEX idx_status (status),
    INDEX idx_type (request_type)
);

-- 4. SERVICE & REPAIR MANAGEMENT TABLES
-- =====================================================

-- Service categories (Maintenance, Repair, Inspection, etc.)
CREATE TABLE service_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Available service types
CREATE TABLE service_types (
    service_id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    service_name VARCHAR(150) NOT NULL,
    description TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    estimated_duration_hours DECIMAL(4,2), -- e.g., 2.5 hours
    is_active BOOLEAN DEFAULT TRUE,
    requires_appointment BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES service_categories(category_id),
    INDEX idx_active (is_active),
    INDEX idx_price (base_price)
);

-- Service/repair appointments/bookings
CREATE TABLE service_bookings (
    booking_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    car_vin VARCHAR(17), -- Customer's car VIN (may not be in our inventory)
    car_make VARCHAR(100),
    car_model VARCHAR(100),
    car_year INT,
    scheduled_date DATE NOT NULL,
    scheduled_time TIME NOT NULL,
    estimated_completion TIMESTAMP,
    actual_completion TIMESTAMP NULL,
    status ENUM('Pending', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'No Show') DEFAULT 'Pending',
    priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    problem_description TEXT,
    customer_notes TEXT,
    mechanic_notes TEXT,
    final_cost DECIMAL(10,2),
    assigned_mechanic_id INT NULL,
    approved_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES service_types(service_id),
    FOREIGN KEY (assigned_mechanic_id) REFERENCES users(user_id),
    FOREIGN KEY (approved_by_admin_id) REFERENCES users(user_id),
    INDEX idx_status (status),
    INDEX idx_date (scheduled_date),
    INDEX idx_mechanic (assigned_mechanic_id)
);

-- Service booking status history (for tracking)
CREATE TABLE booking_status_history (
    history_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    previous_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    changed_by_user_id INT NOT NULL,
    notes TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by_user_id) REFERENCES users(user_id)
);

-- Images uploaded for service issues
CREATE TABLE service_issue_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_description TEXT,
    uploaded_by_user_id INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(user_id)
);

-- 5. REVIEW & RATING SYSTEM
-- =====================================================

-- Service reviews and ratings
CREATE TABLE service_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_title VARCHAR(200),
    review_text TEXT,
    would_recommend BOOLEAN,
    is_verified BOOLEAN DEFAULT FALSE, -- Admin can verify legitimate reviews
    is_featured BOOLEAN DEFAULT FALSE,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    UNIQUE KEY unique_user_booking (user_id, booking_id), -- One review per booking per user
    INDEX idx_rating (rating),
    INDEX idx_verified (is_verified)
);

-- Car/sales reviews (for purchased cars)
CREATE TABLE car_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    user_id INT NOT NULL,
    purchase_request_id INT NULL, -- Link to purchase if available
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_title VARCHAR(200),
    review_text TEXT,
    would_recommend BOOLEAN,
    is_verified BOOLEAN DEFAULT FALSE,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(request_id),
    INDEX idx_rating (rating),
    INDEX idx_verified (is_verified)
);

-- 6. SYSTEM CONFIGURATION & NOTIFICATIONS
-- =====================================================

-- System settings/configuration
CREATE TABLE system_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_description TEXT,
    updated_by_admin_id INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by_admin_id) REFERENCES users(user_id)
);

-- Notifications for users
CREATE TABLE notifications (
    notification_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    notification_type ENUM('Test Drive', 'Purchase', 'Service', 'System', 'Review') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT, -- ID of related record (booking_id, request_id, etc.)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_type (notification_type)
);

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default user roles
INSERT INTO user_roles (role_name, description) VALUES
('Admin', 'Full system access and management'),
('Customer', 'Regular customer with basic access'),
('Mechanic', 'Service technician with repair access'),
('Sales', 'Sales representative with car management access');

-- Insert default service categories
INSERT INTO service_categories (category_name, description) VALUES
('Maintenance', 'Regular maintenance services'),
('Engine Repair', 'Engine-related repairs and diagnostics'),
('Body Work', 'Painting, dent repair, and body services'),
('Electrical', 'Electrical system diagnostics and repair'),
('Transmission', 'Transmission services and repair'),
('Inspection', 'Safety and emissions inspections');

-- Insert sample service types
INSERT INTO service_types (category_id, service_name, description, base_price, estimated_duration_hours) VALUES
(1, 'Oil Change', 'Standard oil change with filter replacement', 75.00, 0.5),
(1, 'Brake Inspection', 'Complete brake system inspection', 95.00, 1.0),
(2, 'Engine Diagnostics', 'Computer diagnostics for engine issues', 125.00, 1.5),
(2, 'Engine Repair', 'General engine repair services', 350.00, 4.0),
(3, 'Paint Touch-up', 'Minor paint touch-up and scratch repair', 200.00, 2.0),
(3, 'Dent Repair', 'Paintless dent repair service', 150.00, 1.5),
(4, 'Electrical Diagnostics', 'Electrical system troubleshooting', 110.00, 1.0),
(5, 'Transmission Service', 'Transmission fluid change and service', 180.00, 2.0),
(6, 'Safety Inspection', 'Annual safety inspection', 50.00, 0.5);

-- Insert sample car categories
INSERT INTO car_categories (category_name, description) VALUES
('Sedan', 'Four-door passenger cars'),
('SUV', 'Sport Utility Vehicles'),
('Hatchback', 'Compact cars with rear hatch'),
('Coupe', 'Two-door sports cars'),
('Convertible', 'Open-top vehicles'),
('Station Wagon', 'Family-oriented cargo vehicles');

-- Insert sample car brands
INSERT INTO car_brands (brand_name) VALUES
('Audi'),
('BMW'),
('Mercedes-Benz'),
('Volkswagen'),
('Porsche');

-- Insert some sample Audi models
INSERT INTO car_models (brand_id, model_name, category_id) VALUES
(1, 'A3', 3), -- Audi A3 Hatchback
(1, 'A4', 1), -- Audi A4 Sedan
(1, 'A6', 1), -- Audi A6 Sedan
(1, 'Q3', 2), -- Audi Q3 SUV
(1, 'Q5', 2), -- Audi Q5 SUV
(1, 'Q7', 2), -- Audi Q7 SUV
(1, 'TT', 4), -- Audi TT Coupe
(1, 'R8', 4); -- Audi R8 Coupe

-- Insert some basic system settings
INSERT INTO system_settings (setting_key, setting_value, setting_description, updated_by_admin_id) VALUES
('site_name', 'Audi Dealership', 'Name of the dealership', 1),
('max_test_drives_per_month', '3', 'Maximum test drives per customer per month', 1),
('service_booking_advance_days', '30', 'How many days in advance can customers book services', 1),
('featured_cars_limit', '12', 'Number of featured cars to display on homepage', 1);

-- =====================================================
-- USEFUL INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional composite indexes for common queries
CREATE INDEX idx_cars_search ON cars (condition_type, fuel_type, price, is_available);
CREATE INDEX idx_bookings_schedule ON service_bookings (scheduled_date, scheduled_time, status);
CREATE INDEX idx_reviews_rating_date ON service_reviews (rating, created_at);
CREATE INDEX idx_user_email_active ON users (email, is_active);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for car listings with full details
CREATE VIEW car_listings_view AS
SELECT 
    c.car_id,
    c.vin,
    b.brand_name,
    m.model_name,
    cat.category_name,
    c.year,
    c.color,
    c.fuel_type,
    c.transmission,
    c.engine_size,
    c.mileage,
    c.condition_type,
    c.price,
    c.is_available,
    c.is_featured,
    c.description,
    c.features,
    c.date_added,
    (SELECT image_url FROM car_images ci WHERE ci.car_id = c.car_id AND ci.is_primary = TRUE LIMIT 1) as primary_image
FROM cars c
JOIN car_models m ON c.model_id = m.model_id
JOIN car_brands b ON m.brand_id = b.brand_id
JOIN car_categories cat ON m.category_id = cat.category_id;

-- View for service bookings with user and service details
CREATE VIEW service_bookings_view AS
SELECT 
    sb.booking_id,
    sb.scheduled_date,
    sb.scheduled_time,
    sb.status,
    sb.priority,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    u.email as customer_email,
    u.phone as customer_phone,
    st.service_name,
    sc.category_name as service_category,
    sb.final_cost,
    CONCAT(mech.first_name, ' ', mech.last_name) as mechanic_name,
    sb.created_at,
    sb.updated_at
FROM service_bookings sb
JOIN users u ON sb.user_id = u.user_id
JOIN service_types st ON sb.service_id = st.service_id
JOIN service_categories sc ON st.category_id = sc.category_id
LEFT JOIN users mech ON sb.assigned_mechanic_id = mech.user_id;