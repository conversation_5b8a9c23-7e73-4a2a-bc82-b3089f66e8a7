-- =====================================================
-- ENHANCED AUTOMOTIVE DATABASE SCHEMA
-- Supports Vehicle Sales, Repair/Maintenance Services,
-- AI Car Recommendations, Chatbot Support, and Restoration Quality Assessment
-- Version: 2.0 - Enhanced with AI Features
-- =====================================================

-- 1. USER MANAGEMENT TABLES
-- =====================================================

-- User roles lookup table
CREATE TABLE user_roles (
    role_id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Main users table
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Germany',
    date_of_birth DATE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES user_roles(role_id)
);

-- User sessions for authentication
CREATE TABLE user_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 2. VEHICLE MANAGEMENT TABLES
-- =====================================================

-- Car categories (SUV, Sedan, Hatchback, etc.)
CREATE TABLE car_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Car brands (Audi, BMW, Mercedes, etc.)
CREATE TABLE car_brands (
    brand_id INT PRIMARY KEY AUTO_INCREMENT,
    brand_name VARCHAR(100) NOT NULL UNIQUE,
    logo_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Car models (A4, A6, Q5, etc.)
CREATE TABLE car_models (
    model_id INT PRIMARY KEY AUTO_INCREMENT,
    brand_id INT NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES car_brands(brand_id),
    FOREIGN KEY (category_id) REFERENCES car_categories(category_id),
    UNIQUE KEY unique_brand_model (brand_id, model_name)
);

-- Main cars table (inventory)
CREATE TABLE cars (
    car_id INT PRIMARY KEY AUTO_INCREMENT,
    model_id INT NOT NULL,
    vin VARCHAR(17) UNIQUE, -- Vehicle Identification Number
    year INT NOT NULL,
    color VARCHAR(50),
    fuel_type ENUM('Gasoline', 'Diesel', 'Electric', 'Hybrid', 'Plug-in Hybrid') NOT NULL,
    transmission ENUM('Manual', 'Automatic', 'CVT') NOT NULL,
    engine_size DECIMAL(3,1), -- e.g., 2.0 for 2.0L
    mileage INT DEFAULT 0, -- in kilometers
    condition_type ENUM('New', 'Used', 'Certified Pre-Owned') NOT NULL,
    condition_description TEXT,
    price DECIMAL(12,2) NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    location VARCHAR(255),
    description TEXT,
    features JSON, -- Store car features as JSON (e.g., ["GPS", "Leather Seats", "Sunroof"])
    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_sold TIMESTAMP NULL,
    sold_to_user_id INT NULL,
    created_by_admin_id INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES car_models(model_id),
    FOREIGN KEY (sold_to_user_id) REFERENCES users(user_id),
    FOREIGN KEY (created_by_admin_id) REFERENCES users(user_id),
    INDEX idx_price (price),
    INDEX idx_condition (condition_type),
    INDEX idx_fuel_type (fuel_type),
    INDEX idx_available (is_available),
    INDEX idx_year (year)
);

-- Car images table
CREATE TABLE car_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 1,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id) ON DELETE CASCADE,
    INDEX idx_primary (car_id, is_primary)
);

-- 3. AI CAR RECOMMENDATION SYSTEM
-- =====================================================

-- Customer preferences for AI recommendations
CREATE TABLE customer_preferences (
    preference_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    budget_min DECIMAL(12,2),
    budget_max DECIMAL(12,2),
    preferred_fuel_types JSON, -- ["Electric", "Hybrid"]
    preferred_categories JSON, -- ["SUV", "Sedan"]
    preferred_brands JSON, -- ["Audi", "BMW"]
    preferred_transmission ENUM('Manual', 'Automatic', 'CVT', 'Any') DEFAULT 'Any',
    max_mileage INT, -- Maximum acceptable mileage
    min_year INT, -- Minimum acceptable year
    required_features JSON, -- ["GPS", "Leather Seats", "Sunroof"]
    preferred_colors JSON, -- ["Black", "White", "Silver"]
    family_size INT, -- Number of people to accommodate
    usage_type ENUM('Daily Commute', 'Family', 'Business', 'Sport', 'Luxury') DEFAULT 'Daily Commute',
    driving_frequency ENUM('Daily', 'Weekly', 'Occasional') DEFAULT 'Daily',
    parking_constraints ENUM('Garage', 'Street', 'Compact Space', 'No Constraints') DEFAULT 'No Constraints',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_budget (budget_min, budget_max)
);

-- AI recommendation sessions and results
CREATE TABLE ai_recommendations (
    recommendation_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    preference_id INT,
    session_id VARCHAR(255), -- To group recommendations from same session
    car_id INT NOT NULL,
    recommendation_score DECIMAL(5,3), -- 0.000 to 1.000 confidence score
    match_reasons JSON, -- ["Budget Match", "Feature Match", "Brand Preference"]
    ai_model_version VARCHAR(50), -- Track which AI model version was used
    recommendation_rank INT, -- 1st, 2nd, 3rd choice etc.
    user_feedback ENUM('Liked', 'Disliked', 'Viewed', 'Contacted', 'Test Drove', 'Purchased') NULL,
    feedback_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    feedback_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (preference_id) REFERENCES customer_preferences(preference_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    INDEX idx_user_session (user_id, session_id),
    INDEX idx_score (recommendation_score DESC),
    INDEX idx_feedback (user_feedback)
);

-- User interaction tracking for AI learning
CREATE TABLE user_car_interactions (
    interaction_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    interaction_type ENUM('View', 'Like', 'Save', 'Share', 'Compare', 'Contact', 'Test Drive Request', 'Purchase Inquiry') NOT NULL,
    interaction_duration_seconds INT, -- How long they viewed the car
    device_type ENUM('Desktop', 'Mobile', 'Tablet') DEFAULT 'Desktop',
    referral_source VARCHAR(255), -- How they found this car
    session_id VARCHAR(255),
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    INDEX idx_user_type (user_id, interaction_type),
    INDEX idx_car_interactions (car_id, interaction_type),
    INDEX idx_session (session_id)
);

-- Car comparison feature for AI insights
CREATE TABLE car_comparisons (
    comparison_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    comparison_name VARCHAR(255),
    car_ids JSON NOT NULL, -- Array of car IDs being compared
    comparison_criteria JSON, -- ["Price", "Fuel Economy", "Features"]
    notes TEXT,
    is_saved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_saved (user_id, is_saved)
);

-- 4. CHATBOT SUPPORT SYSTEM
-- =====================================================

-- Chatbot conversation sessions
CREATE TABLE chatbot_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id INT NULL, -- NULL for anonymous users
    session_type ENUM('Sales Inquiry', 'Service Support', 'General Info', 'Technical Support', 'Complaint') DEFAULT 'General Info',
    language_code VARCHAR(5) DEFAULT 'en',
    is_active BOOLEAN DEFAULT TRUE,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    satisfaction_rating INT NULL CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    escalated_to_human BOOLEAN DEFAULT FALSE,
    escalated_at TIMESTAMP NULL,
    assigned_agent_id INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (assigned_agent_id) REFERENCES users(user_id),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_type_active (session_type, is_active)
);

-- Individual chatbot messages
CREATE TABLE chatbot_messages (
    message_id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(255) NOT NULL,
    sender_type ENUM('User', 'Bot', 'Agent') NOT NULL,
    sender_id INT NULL, -- user_id or agent_id
    message_text TEXT NOT NULL,
    message_type ENUM('Text', 'Quick Reply', 'Card', 'Image', 'File', 'Location') DEFAULT 'Text',
    intent_detected VARCHAR(255), -- AI-detected intent
    confidence_score DECIMAL(5,3), -- AI confidence in intent detection
    entities_extracted JSON, -- Extracted entities like dates, car models, etc.
    suggested_actions JSON, -- Bot's suggested next actions
    attachments JSON, -- File URLs, image URLs, etc.
    is_sensitive BOOLEAN DEFAULT FALSE, -- Contains personal/financial info
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chatbot_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(user_id),
    INDEX idx_session_time (session_id, created_at),
    INDEX idx_intent (intent_detected),
    INDEX idx_sender (sender_type, sender_id)
);

-- Chatbot knowledge base for responses
CREATE TABLE chatbot_knowledge_base (
    kb_id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(100) NOT NULL,
    intent VARCHAR(255) NOT NULL,
    question_patterns JSON NOT NULL, -- Array of question patterns
    response_text TEXT NOT NULL,
    response_type ENUM('Text', 'Quick Reply', 'Card', 'Redirect') DEFAULT 'Text',
    follow_up_questions JSON, -- Suggested follow-up questions
    required_entities JSON, -- Required entities to trigger this response
    priority_score INT DEFAULT 1, -- Higher priority responses shown first
    is_active BOOLEAN DEFAULT TRUE,
    created_by_admin_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_admin_id) REFERENCES users(user_id),
    INDEX idx_intent_active (intent, is_active),
    INDEX idx_category (category),
    INDEX idx_priority (priority_score DESC)
);

-- Chatbot escalation rules
CREATE TABLE chatbot_escalation_rules (
    rule_id INT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(255) NOT NULL,
    trigger_conditions JSON NOT NULL, -- Conditions that trigger escalation
    escalation_type ENUM('Immediate', 'Queue', 'Callback', 'Email') DEFAULT 'Queue',
    target_department ENUM('Sales', 'Service', 'Technical', 'Management') DEFAULT 'Sales',
    priority_level ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_active (is_active)
);

-- 5. SALES & CUSTOMER INTEREST TABLES
-- =====================================================

-- Test drive requests
CREATE TABLE test_drive_requests (
    request_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    requested_date DATE NOT NULL,
    requested_time TIME NOT NULL,
    status ENUM('Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled') DEFAULT 'Pending',
    notes TEXT,
    admin_notes TEXT,
    approved_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (approved_by_admin_id) REFERENCES users(user_id),
    INDEX idx_status (status),
    INDEX idx_date (requested_date)
);

-- Purchase/reservation requests
CREATE TABLE purchase_requests (
    request_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    car_id INT NOT NULL,
    request_type ENUM('Purchase', 'Reservation') NOT NULL,
    offered_price DECIMAL(12,2),
    financing_required BOOLEAN DEFAULT FALSE,
    trade_in_car_details TEXT,
    status ENUM('Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled') DEFAULT 'Pending',
    admin_notes TEXT,
    processed_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (processed_by_admin_id) REFERENCES users(user_id),
    INDEX idx_status (status),
    INDEX idx_type (request_type)
);

-- 6. RESTORATION QUALITY ASSESSMENT SYSTEM
-- =====================================================

-- Restoration projects tracking
CREATE TABLE restoration_projects (
    project_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    customer_id INT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    project_type ENUM('Full Restoration', 'Partial Restoration', 'Repair', 'Customization', 'Maintenance') NOT NULL,
    project_status ENUM('Planning', 'In Progress', 'Quality Review', 'Completed', 'On Hold', 'Cancelled') DEFAULT 'Planning',
    estimated_start_date DATE,
    actual_start_date DATE,
    estimated_completion_date DATE,
    actual_completion_date DATE,
    estimated_cost DECIMAL(12,2),
    actual_cost DECIMAL(12,2),
    project_description TEXT,
    special_requirements TEXT,
    assigned_team_lead_id INT,
    quality_score DECIMAL(4,2), -- Overall quality score (0.00 to 10.00)
    customer_satisfaction_rating INT CHECK (customer_satisfaction_rating >= 1 AND customer_satisfaction_rating <= 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (customer_id) REFERENCES users(user_id),
    FOREIGN KEY (assigned_team_lead_id) REFERENCES users(user_id),
    INDEX idx_status (project_status),
    INDEX idx_customer (customer_id),
    INDEX idx_dates (estimated_completion_date, actual_completion_date)
);

-- Restoration phases/stages
CREATE TABLE restoration_phases (
    phase_id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    phase_name VARCHAR(255) NOT NULL,
    phase_order INT NOT NULL,
    phase_description TEXT,
    estimated_duration_days INT,
    actual_duration_days INT,
    phase_status ENUM('Not Started', 'In Progress', 'Completed', 'On Hold', 'Skipped') DEFAULT 'Not Started',
    quality_rating DECIMAL(4,2), -- Quality rating for this phase (0.00 to 10.00)
    notes TEXT,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    assigned_technician_id INT,
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_technician_id) REFERENCES users(user_id),
    INDEX idx_project_order (project_id, phase_order),
    INDEX idx_status (phase_status)
);

-- Before/After photo management for quality assessment
CREATE TABLE restoration_photos (
    photo_id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    phase_id INT NULL, -- NULL for overall project photos
    photo_type ENUM('Before', 'During', 'After', 'Detail', 'Problem', 'Solution') NOT NULL,
    photo_category ENUM('Exterior', 'Interior', 'Engine', 'Undercarriage', 'Wheels', 'Paint', 'Upholstery', 'Dashboard', 'Other') NOT NULL,
    photo_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    photo_description TEXT,
    photo_angle VARCHAR(100), -- 'Front', 'Rear', 'Driver Side', 'Passenger Side', 'Top', 'Detail'
    lighting_conditions VARCHAR(100), -- 'Natural', 'Studio', 'Garage', 'Outdoor'
    camera_settings JSON, -- Store camera metadata
    ai_quality_score DECIMAL(5,3), -- AI-assessed photo quality (0.000 to 1.000)
    ai_damage_assessment JSON, -- AI-detected damage/issues
    ai_completion_percentage DECIMAL(5,2), -- AI-estimated completion percentage
    is_featured BOOLEAN DEFAULT FALSE,
    taken_by_user_id INT NOT NULL,
    taken_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (phase_id) REFERENCES restoration_phases(phase_id),
    FOREIGN KEY (taken_by_user_id) REFERENCES users(user_id),
    INDEX idx_project_type (project_id, photo_type),
    INDEX idx_category (photo_category),
    INDEX idx_quality (ai_quality_score DESC)
);

-- Quality assessment criteria and scoring
CREATE TABLE quality_assessment_criteria (
    criteria_id INT PRIMARY KEY AUTO_INCREMENT,
    criteria_name VARCHAR(255) NOT NULL,
    criteria_category ENUM('Paint', 'Body', 'Interior', 'Mechanical', 'Electrical', 'Overall') NOT NULL,
    description TEXT,
    weight_percentage DECIMAL(5,2) NOT NULL, -- How much this criteria contributes to overall score
    scoring_method ENUM('Numeric', 'Pass/Fail', 'Percentage', 'Grade') DEFAULT 'Numeric',
    min_score DECIMAL(4,2) DEFAULT 0.00,
    max_score DECIMAL(4,2) DEFAULT 10.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (criteria_category),
    INDEX idx_active (is_active)
);

-- Individual quality assessments
CREATE TABLE quality_assessments (
    assessment_id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    phase_id INT NULL,
    criteria_id INT NOT NULL,
    assessor_id INT NOT NULL, -- User who performed the assessment
    assessment_type ENUM('Self Assessment', 'Peer Review', 'Customer Review', 'Final Inspection') NOT NULL,
    score DECIMAL(4,2) NOT NULL,
    notes TEXT,
    before_photo_id INT NULL,
    after_photo_id INT NULL,
    assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (phase_id) REFERENCES restoration_phases(phase_id),
    FOREIGN KEY (criteria_id) REFERENCES quality_assessment_criteria(criteria_id),
    FOREIGN KEY (assessor_id) REFERENCES users(user_id),
    FOREIGN KEY (before_photo_id) REFERENCES restoration_photos(photo_id),
    FOREIGN KEY (after_photo_id) REFERENCES restoration_photos(photo_id),
    INDEX idx_project_type (project_id, assessment_type),
    INDEX idx_score (score),
    INDEX idx_date (assessment_date)
);

-- AI-powered photo comparison and analysis
CREATE TABLE ai_photo_analysis (
    analysis_id INT PRIMARY KEY AUTO_INCREMENT,
    before_photo_id INT NOT NULL,
    after_photo_id INT NOT NULL,
    project_id INT NOT NULL,
    ai_model_version VARCHAR(50) NOT NULL,
    improvement_score DECIMAL(5,3), -- 0.000 to 1.000 improvement rating
    detected_improvements JSON, -- Array of improvements detected
    detected_issues JSON, -- Array of remaining issues
    color_accuracy_score DECIMAL(5,3),
    surface_quality_score DECIMAL(5,3),
    alignment_score DECIMAL(5,3),
    completeness_score DECIMAL(5,3),
    overall_quality_score DECIMAL(5,3),
    confidence_level DECIMAL(5,3), -- AI confidence in analysis
    processing_time_ms INT, -- Time taken for analysis
    analysis_metadata JSON, -- Additional AI analysis data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (before_photo_id) REFERENCES restoration_photos(photo_id),
    FOREIGN KEY (after_photo_id) REFERENCES restoration_photos(photo_id),
    FOREIGN KEY (project_id) REFERENCES restoration_projects(project_id) ON DELETE CASCADE,
    INDEX idx_project (project_id),
    INDEX idx_quality (overall_quality_score DESC),
    INDEX idx_confidence (confidence_level DESC)
);

-- 7. SERVICE & REPAIR MANAGEMENT TABLES
-- =====================================================

-- Service categories (Maintenance, Repair, Inspection, etc.)
CREATE TABLE service_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Available service types
CREATE TABLE service_types (
    service_id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    service_name VARCHAR(150) NOT NULL,
    description TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    estimated_duration_hours DECIMAL(4,2), -- e.g., 2.5 hours
    is_active BOOLEAN DEFAULT TRUE,
    requires_appointment BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES service_categories(category_id),
    INDEX idx_active (is_active),
    INDEX idx_price (base_price)
);

-- Service/repair appointments/bookings
CREATE TABLE service_bookings (
    booking_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    car_vin VARCHAR(17), -- Customer's car VIN (may not be in our inventory)
    car_make VARCHAR(100),
    car_model VARCHAR(100),
    car_year INT,
    scheduled_date DATE NOT NULL,
    scheduled_time TIME NOT NULL,
    estimated_completion TIMESTAMP,
    actual_completion TIMESTAMP NULL,
    status ENUM('Pending', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'No Show') DEFAULT 'Pending',
    priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    problem_description TEXT,
    customer_notes TEXT,
    mechanic_notes TEXT,
    final_cost DECIMAL(10,2),
    assigned_mechanic_id INT NULL,
    approved_by_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES service_types(service_id),
    FOREIGN KEY (assigned_mechanic_id) REFERENCES users(user_id),
    FOREIGN KEY (approved_by_admin_id) REFERENCES users(user_id),
    INDEX idx_status (status),
    INDEX idx_date (scheduled_date),
    INDEX idx_mechanic (assigned_mechanic_id)
);

-- Service booking status history (for tracking)
CREATE TABLE booking_status_history (
    history_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    previous_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    changed_by_user_id INT NOT NULL,
    notes TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by_user_id) REFERENCES users(user_id)
);

-- Images uploaded for service issues
CREATE TABLE service_issue_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_description TEXT,
    uploaded_by_user_id INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(user_id)
);

-- 8. FINANCIAL & PAYMENT MANAGEMENT
-- =====================================================

-- Payment methods
CREATE TABLE payment_methods (
    payment_method_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    method_type ENUM('Credit Card', 'Debit Card', 'Bank Transfer', 'PayPal', 'Financing', 'Cash') NOT NULL,
    provider VARCHAR(100), -- Visa, MasterCard, PayPal, etc.
    last_four_digits VARCHAR(4),
    expiry_month INT,
    expiry_year INT,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active)
);

-- Financial transactions
CREATE TABLE transactions (
    transaction_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    transaction_type ENUM('Car Purchase', 'Service Payment', 'Restoration Payment', 'Deposit', 'Refund', 'Fee') NOT NULL,
    related_id INT, -- car_id, booking_id, project_id, etc.
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'EUR',
    payment_method_id INT,
    transaction_status ENUM('Pending', 'Processing', 'Completed', 'Failed', 'Cancelled', 'Refunded') DEFAULT 'Pending',
    payment_gateway VARCHAR(100), -- Stripe, PayPal, etc.
    gateway_transaction_id VARCHAR(255),
    description TEXT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(payment_method_id),
    INDEX idx_user_type (user_id, transaction_type),
    INDEX idx_status (transaction_status),
    INDEX idx_amount (amount)
);

-- 9. AUDIT & LOGGING SYSTEM
-- =====================================================

-- System audit log
CREATE TABLE audit_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    action_type ENUM('INSERT', 'UPDATE', 'DELETE', 'SELECT') NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_created_at (created_at)
);

-- Error logs
CREATE TABLE error_logs (
    error_id INT PRIMARY KEY AUTO_INCREMENT,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    user_id INT NULL,
    request_url VARCHAR(500),
    request_method VARCHAR(10),
    request_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    severity ENUM('Low', 'Medium', 'High', 'Critical') DEFAULT 'Medium',
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by_admin_id INT NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (resolved_by_admin_id) REFERENCES users(user_id),
    INDEX idx_severity_resolved (severity, is_resolved),
    INDEX idx_created_at (created_at)
);

-- 10. REVIEW & RATING SYSTEM
-- =====================================================

-- Service reviews and ratings
CREATE TABLE service_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_title VARCHAR(200),
    review_text TEXT,
    would_recommend BOOLEAN,
    is_verified BOOLEAN DEFAULT FALSE, -- Admin can verify legitimate reviews
    is_featured BOOLEAN DEFAULT FALSE,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES service_bookings(booking_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    UNIQUE KEY unique_user_booking (user_id, booking_id), -- One review per booking per user
    INDEX idx_rating (rating),
    INDEX idx_verified (is_verified)
);

-- Car/sales reviews (for purchased cars)
CREATE TABLE car_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT,
    car_id INT NOT NULL,
    user_id INT NOT NULL,
    purchase_request_id INT NULL, -- Link to purchase if available
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_title VARCHAR(200),
    review_text TEXT,
    would_recommend BOOLEAN,
    is_verified BOOLEAN DEFAULT FALSE,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (car_id) REFERENCES cars(car_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (purchase_request_id) REFERENCES purchase_requests(request_id),
    INDEX idx_rating (rating),
    INDEX idx_verified (is_verified)
);

-- 11. SYSTEM CONFIGURATION & NOTIFICATIONS
-- =====================================================

-- System settings/configuration
CREATE TABLE system_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_description TEXT,
    updated_by_admin_id INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by_admin_id) REFERENCES users(user_id)
);

-- Notifications for users
CREATE TABLE notifications (
    notification_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    notification_type ENUM('Test Drive', 'Purchase', 'Service', 'System', 'Review') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT, -- ID of related record (booking_id, request_id, etc.)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_type (notification_type)
);

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default user roles
INSERT INTO user_roles (role_name, description) VALUES
('Admin', 'Full system access and management'),
('Customer', 'Regular customer with basic access'),
('Mechanic', 'Service technician with repair access'),
('Sales', 'Sales representative with car management access');

-- Insert default service categories
INSERT INTO service_categories (category_name, description) VALUES
('Maintenance', 'Regular maintenance services'),
('Engine Repair', 'Engine-related repairs and diagnostics'),
('Body Work', 'Painting, dent repair, and body services'),
('Electrical', 'Electrical system diagnostics and repair'),
('Transmission', 'Transmission services and repair'),
('Inspection', 'Safety and emissions inspections');

-- Insert sample service types
INSERT INTO service_types (category_id, service_name, description, base_price, estimated_duration_hours) VALUES
(1, 'Oil Change', 'Standard oil change with filter replacement', 75.00, 0.5),
(1, 'Brake Inspection', 'Complete brake system inspection', 95.00, 1.0),
(2, 'Engine Diagnostics', 'Computer diagnostics for engine issues', 125.00, 1.5),
(2, 'Engine Repair', 'General engine repair services', 350.00, 4.0),
(3, 'Paint Touch-up', 'Minor paint touch-up and scratch repair', 200.00, 2.0),
(3, 'Dent Repair', 'Paintless dent repair service', 150.00, 1.5),
(4, 'Electrical Diagnostics', 'Electrical system troubleshooting', 110.00, 1.0),
(5, 'Transmission Service', 'Transmission fluid change and service', 180.00, 2.0),
(6, 'Safety Inspection', 'Annual safety inspection', 50.00, 0.5);

-- Insert sample car categories
INSERT INTO car_categories (category_name, description) VALUES
('Sedan', 'Four-door passenger cars'),
('SUV', 'Sport Utility Vehicles'),
('Hatchback', 'Compact cars with rear hatch'),
('Coupe', 'Two-door sports cars'),
('Convertible', 'Open-top vehicles'),
('Station Wagon', 'Family-oriented cargo vehicles');

-- Insert sample car brands
INSERT INTO car_brands (brand_name) VALUES
('Audi'),
('BMW'),
('Mercedes-Benz'),
('Volkswagen'),
('Porsche');

-- Insert some sample Audi models
INSERT INTO car_models (brand_id, model_name, category_id) VALUES
(1, 'A3', 3), -- Audi A3 Hatchback
(1, 'A4', 1), -- Audi A4 Sedan
(1, 'A6', 1), -- Audi A6 Sedan
(1, 'Q3', 2), -- Audi Q3 SUV
(1, 'Q5', 2), -- Audi Q5 SUV
(1, 'Q7', 2), -- Audi Q7 SUV
(1, 'TT', 4), -- Audi TT Coupe
(1, 'R8', 4); -- Audi R8 Coupe

-- Insert enhanced system settings
INSERT INTO system_settings (setting_key, setting_value, setting_description, updated_by_admin_id) VALUES
('site_name', 'Audi Dealership', 'Name of the dealership', 1),
('max_test_drives_per_month', '3', 'Maximum test drives per customer per month', 1),
('service_booking_advance_days', '30', 'How many days in advance can customers book services', 1),
('featured_cars_limit', '12', 'Number of featured cars to display on homepage', 1),
('ai_recommendation_enabled', 'true', 'Enable AI car recommendation system', 1),
('ai_recommendation_max_results', '10', 'Maximum number of AI recommendations to show', 1),
('chatbot_enabled', 'true', 'Enable chatbot support system', 1),
('chatbot_escalation_threshold', '3', 'Number of failed responses before escalating to human', 1),
('restoration_quality_threshold', '8.0', 'Minimum quality score for restoration completion', 1),
('ai_photo_analysis_enabled', 'true', 'Enable AI-powered photo analysis for restorations', 1);

-- Insert default quality assessment criteria
INSERT INTO quality_assessment_criteria (criteria_name, criteria_category, description, weight_percentage, max_score) VALUES
('Paint Quality', 'Paint', 'Overall paint finish, color match, and surface smoothness', 25.00, 10.00),
('Body Alignment', 'Body', 'Panel gaps, alignment, and structural integrity', 20.00, 10.00),
('Interior Condition', 'Interior', 'Upholstery, dashboard, and interior components', 15.00, 10.00),
('Engine Performance', 'Mechanical', 'Engine operation, performance, and reliability', 20.00, 10.00),
('Electrical Systems', 'Electrical', 'All electrical components and systems functionality', 10.00, 10.00),
('Overall Craftsmanship', 'Overall', 'General workmanship and attention to detail', 10.00, 10.00);

-- Insert sample chatbot knowledge base entries
INSERT INTO chatbot_knowledge_base (category, intent, question_patterns, response_text, response_type, created_by_admin_id) VALUES
('Sales', 'car_inquiry', '["What cars do you have?", "Show me available cars", "Car inventory"]', 'I can help you find the perfect car! What type of vehicle are you looking for? We have sedans, SUVs, coupes, and more available.', 'Text', 1),
('Sales', 'price_inquiry', '["How much does it cost?", "What is the price?", "Car pricing"]', 'Car prices vary based on model, year, and condition. Would you like me to show you cars within a specific budget range?', 'Text', 1),
('Service', 'service_booking', '["Book service", "Schedule appointment", "Service booking"]', 'I can help you schedule a service appointment. What type of service do you need? Oil change, brake inspection, or something else?', 'Text', 1),
('Service', 'service_hours', '["What are your hours?", "When are you open?", "Service hours"]', 'Our service department is open Monday-Friday 8AM-6PM, and Saturday 8AM-4PM. We are closed on Sundays.', 'Text', 1),
('General', 'greeting', '["Hello", "Hi", "Hey", "Good morning"]', 'Hello! Welcome to Audi Dealership. I am here to help you with car sales, service appointments, and general inquiries. How can I assist you today?', 'Text', 1);

-- Insert sample escalation rules
INSERT INTO chatbot_escalation_rules (rule_name, trigger_conditions, escalation_type, target_department, priority_level) VALUES
('Complex Technical Issues', '{"keywords": ["engine problem", "transmission issue", "electrical fault"], "confidence_threshold": 0.3}', 'Queue', 'Technical', 'High'),
('Purchase Intent', '{"keywords": ["buy", "purchase", "financing"], "confidence_threshold": 0.7}', 'Immediate', 'Sales', 'Medium'),
('Service Complaints', '{"keywords": ["complaint", "unsatisfied", "poor service"], "sentiment": "negative"}', 'Immediate', 'Management', 'High'),
('Multiple Failed Responses', '{"failed_responses": 3}', 'Queue', 'Sales', 'Medium');

-- =====================================================
-- USEFUL INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional composite indexes for common queries
CREATE INDEX idx_cars_search ON cars (condition_type, fuel_type, price, is_available);
CREATE INDEX idx_bookings_schedule ON service_bookings (scheduled_date, scheduled_time, status);
CREATE INDEX idx_reviews_rating_date ON service_reviews (rating, created_at);
CREATE INDEX idx_user_email_active ON users (email, is_active);

-- AI and enhanced feature indexes
CREATE INDEX idx_ai_recommendations_score_rank ON ai_recommendations (recommendation_score DESC, recommendation_rank);
CREATE INDEX idx_user_interactions_type_time ON user_car_interactions (user_id, interaction_type, created_at);
CREATE INDEX idx_chatbot_sessions_active_type ON chatbot_sessions (is_active, session_type, started_at);
CREATE INDEX idx_chatbot_messages_session_time ON chatbot_messages (session_id, created_at);
CREATE INDEX idx_restoration_projects_status_customer ON restoration_projects (project_status, customer_id);
CREATE INDEX idx_restoration_photos_project_type ON restoration_photos (project_id, photo_type, taken_at);
CREATE INDEX idx_quality_assessments_project_score ON quality_assessments (project_id, score DESC);
CREATE INDEX idx_transactions_user_status_amount ON transactions (user_id, transaction_status, amount);
CREATE INDEX idx_audit_log_table_action_time ON audit_log (table_name, action_type, created_at);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for car listings with full details
CREATE VIEW car_listings_view AS
SELECT 
    c.car_id,
    c.vin,
    b.brand_name,
    m.model_name,
    cat.category_name,
    c.year,
    c.color,
    c.fuel_type,
    c.transmission,
    c.engine_size,
    c.mileage,
    c.condition_type,
    c.price,
    c.is_available,
    c.is_featured,
    c.description,
    c.features,
    c.date_added,
    (SELECT image_url FROM car_images ci WHERE ci.car_id = c.car_id AND ci.is_primary = TRUE LIMIT 1) as primary_image
FROM cars c
JOIN car_models m ON c.model_id = m.model_id
JOIN car_brands b ON m.brand_id = b.brand_id
JOIN car_categories cat ON m.category_id = cat.category_id;

-- View for service bookings with user and service details
CREATE VIEW service_bookings_view AS
SELECT
    sb.booking_id,
    sb.scheduled_date,
    sb.scheduled_time,
    sb.status,
    sb.priority,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    u.email as customer_email,
    u.phone as customer_phone,
    st.service_name,
    sc.category_name as service_category,
    sb.final_cost,
    CONCAT(mech.first_name, ' ', mech.last_name) as mechanic_name,
    sb.created_at,
    sb.updated_at
FROM service_bookings sb
JOIN users u ON sb.user_id = u.user_id
JOIN service_types st ON sb.service_id = st.service_id
JOIN service_categories sc ON st.category_id = sc.category_id
LEFT JOIN users mech ON sb.assigned_mechanic_id = mech.user_id;

-- View for AI recommendations with car details
CREATE VIEW ai_recommendations_view AS
SELECT
    ar.recommendation_id,
    ar.user_id,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    ar.session_id,
    ar.recommendation_score,
    ar.recommendation_rank,
    ar.match_reasons,
    ar.user_feedback,
    c.car_id,
    b.brand_name,
    m.model_name,
    cat.category_name,
    c.year,
    c.price,
    c.fuel_type,
    c.condition_type,
    ar.created_at
FROM ai_recommendations ar
JOIN users u ON ar.user_id = u.user_id
JOIN cars c ON ar.car_id = c.car_id
JOIN car_models m ON c.model_id = m.model_id
JOIN car_brands b ON m.brand_id = b.brand_id
JOIN car_categories cat ON m.category_id = cat.category_id;

-- View for restoration projects with quality metrics
CREATE VIEW restoration_projects_view AS
SELECT
    rp.project_id,
    rp.project_name,
    rp.project_type,
    rp.project_status,
    CONCAT(customer.first_name, ' ', customer.last_name) as customer_name,
    CONCAT(lead.first_name, ' ', lead.last_name) as team_lead_name,
    b.brand_name,
    m.model_name,
    c.year,
    rp.estimated_cost,
    rp.actual_cost,
    rp.quality_score,
    rp.customer_satisfaction_rating,
    rp.estimated_completion_date,
    rp.actual_completion_date,
    (SELECT COUNT(*) FROM restoration_photos rph WHERE rph.project_id = rp.project_id AND rph.photo_type = 'Before') as before_photos_count,
    (SELECT COUNT(*) FROM restoration_photos rph WHERE rph.project_id = rp.project_id AND rph.photo_type = 'After') as after_photos_count,
    (SELECT AVG(qa.score) FROM quality_assessments qa WHERE qa.project_id = rp.project_id) as avg_quality_score,
    rp.created_at,
    rp.updated_at
FROM restoration_projects rp
JOIN cars c ON rp.car_id = c.car_id
JOIN car_models m ON c.model_id = m.model_id
JOIN car_brands b ON m.brand_id = b.brand_id
JOIN users customer ON rp.customer_id = customer.user_id
LEFT JOIN users lead ON rp.assigned_team_lead_id = lead.user_id;

-- View for chatbot session analytics
CREATE VIEW chatbot_analytics_view AS
SELECT
    cs.session_id,
    cs.session_type,
    cs.language_code,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    cs.satisfaction_rating,
    cs.escalated_to_human,
    CONCAT(agent.first_name, ' ', agent.last_name) as assigned_agent_name,
    cs.started_at,
    cs.ended_at,
    TIMESTAMPDIFF(MINUTE, cs.started_at, cs.ended_at) as session_duration_minutes,
    (SELECT COUNT(*) FROM chatbot_messages cm WHERE cm.session_id = cs.session_id AND cm.sender_type = 'User') as user_messages_count,
    (SELECT COUNT(*) FROM chatbot_messages cm WHERE cm.session_id = cs.session_id AND cm.sender_type = 'Bot') as bot_messages_count,
    (SELECT AVG(cm.confidence_score) FROM chatbot_messages cm WHERE cm.session_id = cs.session_id AND cm.sender_type = 'Bot') as avg_bot_confidence
FROM chatbot_sessions cs
LEFT JOIN users u ON cs.user_id = u.user_id
LEFT JOIN users agent ON cs.assigned_agent_id = agent.user_id;